"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Bell,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import {
  getClassNotifications,
  getStudentNotifications,
  getClassUnreadCount,
  getStudentUnreadCount,
  markClassNotificationAsRead,
  markStudentNotificationAsRead,
  markAllClassNotificationsAsRead,
  markAllStudentNotificationsAsRead,
  deleteAllClassNotifications,
  deleteAllStudentNotifications,
  Notification,
  NotificationResponse
} from '@/services/notificationService';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useAuth } from '@/lib/useAuth';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [tempSearchTerm, setTempSearchTerm] = useState('');
  const [tempFilterType, setTempFilterType] = useState<string>('all');
  const [tempFilterStatus, setTempFilterStatus] = useState<string>('all');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showMarkAllDialog, setShowMarkAllDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const router = useRouter();
  const { user } = useAuth();

  const [userType, setUserType] = useState<'student' | 'class' | null>(null);

  useEffect(() => {
    if (user) {
      setUserType('class');
      return;
    }
    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
      setUserType('student');
      return;
    }
    setUserType(null);
  }, [user]);

  // Ensure notifications is always an array
  const safeNotifications = useMemo(() => Array.isArray(notifications) ? notifications : [], [notifications]);
  const safeFilteredNotifications = useMemo(() => Array.isArray(filteredNotifications) ? filteredNotifications : [], [filteredNotifications]);

  const fetchNotifications = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);

      if (!userType || !user) {
        setNotifications([]);
        setFilteredNotifications([]);
        return;
      }

      let result: NotificationResponse;
      let count: number;

      if (userType === 'class') {
        [result, count] = await Promise.all([
          getClassNotifications(page, 10),
          getClassUnreadCount()
        ]);
      } else if (userType === 'student') {
        [result, count] = await Promise.all([
          getStudentNotifications(page, 10),
          getStudentUnreadCount()
        ]);
      } else {
        setNotifications([]);
        setFilteredNotifications([]);
        return;
      }

      // Handle different response formats
      let notificationsList: Notification[] = [];

      if (Array.isArray(result)) {
        notificationsList = result;
      } else if (result?.notifications && Array.isArray(result.notifications)) {
        notificationsList = result.notifications;
      } else {
        notificationsList = [];
      }
      
      setNotifications(notificationsList);
      setFilteredNotifications(notificationsList);
      setUnreadCount(count || 0);
      
      // Set pagination data if available
      if (result?.pagination) {
        setCurrentPage(result.pagination.currentPage);
        setTotalPages(result.pagination.totalPages);
        setTotalCount(result.pagination.totalCount);
      }
    } catch (error) {
      console.error('❌ Error fetching notifications:', error);
      toast.error('Failed to fetch notifications');
      // Set empty arrays on error
      setNotifications([]);
      setFilteredNotifications([]);
    } finally {
      setLoading(false);
    }
  }, [userType, user]);

  useEffect(() => {
    if (userType) {
      fetchNotifications();
      const interval = setInterval(() => fetchNotifications(1), 30000);
      return () => clearInterval(interval);
    }
  }, [userType, fetchNotifications]);

  // Handle page changes
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      fetchNotifications(page);
    }
  };

  // Apply filters function
  const applyFilters = () => {
    setSearchTerm(tempSearchTerm);
    setFilterType(tempFilterType);
    setFilterStatus(tempFilterStatus);
  };

  // Clear filters function
  const clearFilters = () => {
    setTempSearchTerm('');
    setTempFilterType('all');
    setTempFilterStatus('all');
    setSearchTerm('');
    setFilterType('all');
    setFilterStatus('all');
  };

  useEffect(() => {
    let filtered = [...safeNotifications];

    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(notification => notification.type === filterType);
    }

    if (filterStatus === 'read') {
      filtered = filtered.filter(notification => notification.isRead);
    } else if (filterStatus === 'unread') {
      filtered = filtered.filter(notification => !notification.isRead);
    }

    setFilteredNotifications(filtered);
  }, [safeNotifications, searchTerm, filterType, filterStatus]);

  const handleNotificationClick = async (notification: Notification) => {
    try {
      if (!notification.isRead) {
        if (userType === 'class') {
          await markClassNotificationAsRead(notification.id);
        } else {
          await markStudentNotificationAsRead(notification.id);
        }
        
        setNotifications(prev => {
          if (!Array.isArray(prev)) return [];
          return prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n);
        });
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      // Handle notification click actions based on type
      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {
        router.push(notification.data.redirectUrl);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      if (userType === 'class') {
        await markAllClassNotificationsAsRead();
      } else {
        await markAllStudentNotificationsAsRead();
      }
      
      setNotifications(prev => {
        if (!Array.isArray(prev)) return [];
        return prev.map(n => ({ ...n, isRead: true }));
      });
      setUnreadCount(0);
      setShowMarkAllDialog(false);
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  const handleDeleteAll = async () => {
    try {
      if (userType === 'class') {
        await deleteAllClassNotifications();
      } else {
        await deleteAllStudentNotifications();
      }
      
      setNotifications([]);
      setFilteredNotifications([]);
      setUnreadCount(0);
      setShowDeleteDialog(false);
      toast.success('All notifications deleted successfully');
    } catch (error) {
      console.error('Error deleting all notifications:', error);
      toast.error('Failed to delete all notifications');
    }
  };

  const stats = [
    {
      title: 'Total Notifications',
      value: totalCount || 0, 
      icon: Bell,
      color: 'text-blue-600'
    },
    {
      title: 'Unread',
      value: unreadCount,
      icon: Clock,
      color: 'text-red-600'
    },
    {
      title: 'Read',
      value: Math.max(0, (totalCount || 0) - unreadCount),
      icon: CheckCircle,
      color: 'text-green-600'
    }
  ];

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading notifications...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!userType) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground">Please log in to view notifications.</p>
        </div>
      </div>
    );
  }

  return (
    <>
    <Header/>
    <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Notifications</h1>
            <p className="text-muted-foreground">Manage all your notifications</p>
          </div>
          <div className="flex gap-2">
            {unreadCount > 0 && (
              <Button
                variant="outline"
                onClick={() => setShowMarkAllDialog(true)}
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Mark All Read
              </Button>
            )}
            {safeNotifications.length > 0 && unreadCount === 0 && (
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(true)}
                className="flex items-center gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
                Delete All
              </Button>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${stat.color}`}>
                  {stat.value}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search notifications..."
                      value={tempSearchTerm}
                      onChange={(e) => setTempSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={tempFilterStatus} onValueChange={setTempFilterStatus}>
                  <SelectTrigger className="w-full md:w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="unread">Unread</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button onClick={applyFilters} className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Apply Filters
                </Button>
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="flex items-center gap-2"
                >
                  Clear All
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications (Page {currentPage} of {totalPages} - {safeFilteredNotifications.length} shown)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {safeFilteredNotifications.length === 0 ? (
              <div className="text-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-muted-foreground mb-2">
                  {searchTerm || filterStatus !== 'all'
                    ? 'No notifications match your filters'
                    : 'No notifications yet'
                  }
                </h3>
                <p className="text-sm text-muted-foreground">
                  {searchTerm || filterStatus !== 'all'
                    ? 'Try adjusting your search or filter criteria'
                    : 'New notifications will appear here when they arrive'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {safeFilteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      !notification.isRead
                        ? 'bg-blue-50/50 border-blue-200 hover:bg-blue-50'
                        : 'bg-white hover:bg-gray-50'
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start gap-4">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        !notification.isRead ? 'bg-blue-500' : 'bg-gray-300'
                      }`} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-semibold text-sm">{notification.title}</h3>
                              {!notification.isRead && (
                                <div className="w-2 h-2 rounded-full bg-blue-500 flex-shrink-0" />
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {notification.isRead ? (
                              <Badge variant="outline" className="text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Read
                              </Badge>
                            ) : (
                              <Badge variant="default" className="text-xs bg-blue-600">
                                <Clock className="h-3 w-3 mr-1" />
                                New
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} notifications
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1 || loading}
                  className="h-8 w-8"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="h-8 w-8"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm px-3 py-1 bg-gray-100 rounded">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  className="h-8 w-8"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages || loading}
                  className="h-8 w-8"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </Card>

        {/* Mark All Read Dialog */}
        <AlertDialog open={showMarkAllDialog} onOpenChange={setShowMarkAllDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Mark All Notifications as Read</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to mark all {unreadCount} unread notifications as read?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleMarkAllAsRead}>
                Mark All Read
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Delete All Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete All Notifications</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete all notifications? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteAll}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete All
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
    <Footer/>
    </>
  );
}
