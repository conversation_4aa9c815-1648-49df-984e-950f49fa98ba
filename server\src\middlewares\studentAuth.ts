import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';


export const studentAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    console.log('🔍 Student Auth Middleware - Headers:', req.headers.authorization);
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ No valid auth header found');
      sendError(res, 'Authentication required', 401);
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      console.log('❌ No token found in auth header');
      sendError(res, 'Authentication required', 401);
      return;
    }
    console.log('🔑 Token found:', token.substring(0, 20) + '...');

    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; contactNo: string; isVerified: boolean };
    console.log('🔓 Token decoded successfully:', { id: decoded.id, contactNo: decoded.contactNo });
    if (!decoded || !decoded.id) {
      console.log('❌ Invalid decoded token structure');
      sendError(res, 'Invalid token', 401);
      return;
    }

    const student = await prisma.student.findUnique({
      where: { id: decoded.id },
      select: { id: true, email: true, contact: true }
    });
    console.log('👤 Student found:', student ? 'Yes' : 'No');

    if (!student) {
      console.log('❌ Student not found in database');
      sendError(res, 'Student not found', 401);
      return;
    }

    req.student = {
      id: student.id,
      email: student.email ?? '',
      contactNo: student.contact ?? ''
    };
    console.log('✅ Student authentication successful');

    next();
  } catch (error) {
    console.error('❌ Auth middleware error:', error);
    sendError(res, 'Authentication failed', 401);
  }
};