import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';


export const studentAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; contactNo: string; isVerified: boolean };
    if (!decoded || !decoded.id) {
      sendError(res, 'Invalid token', 401);
      return;
    }

    const student = await prisma.student.findUnique({
      where: { id: decoded.id },
      select: { id: true, email: true, contact: true }
    });

    if (!student) {
      sendError(res, 'Student not found', 401);
      return;
    }

    req.student = {
      id: student.id,
      email: student.email ?? '',
      contactNo: student.contact ?? ''
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    sendError(res, 'Authentication failed', 401);
  }
};